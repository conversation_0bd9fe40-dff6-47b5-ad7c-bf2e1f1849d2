var sampleOperationResp = require('./utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('./utils/db_resp');
const users_model = require('./users_model');
const {
    setParamsToUserModel,
    setParamsToSubtaskModel,
    getParamsToServiceModel,
    getParamsToSubtaskModel,
    setParamsToOrganisationsModel,
} = require('./queues/processors/helpers');
const {
    translateAPIParamsToFrontendKeys,
} = require('../form_schemas/subtasks');
const { default: subtasks_workflow } = require('./workflows/subtasks_workflow');
const {
    getSbtskWorkflowModel,
} = require('./queues_v2/processors/helpers/sbtsk_workflow_helper');
const {
    getServiceWorkflowModel,
} = require('./queues_v2/processors/helpers/services_workflow_helper');

class brand_model {
    async validateCollabOrderidIsExists(req, collab_order_id_) {
        let org_id = parseInt(req.user_details.org.id);
        let srvc_type_id = parseInt(req.params.service_type_id);
        try {
            //Returns true if the collaboration order ID exists, false otherwise
            const respData = (
                await this.db.tms_hlpr_is_collab_order_id_exists(
                    org_id,
                    srvc_type_id,
                    collab_order_id_.toString()
                )
            )?.[0].tms_hlpr_is_collab_order_id_exists;

            return respData;
        } catch (error) {
            console.log('validateCollabOrderidIsExists failed', error);
            return 'Failed';
        }
    }

    async getSrvcTypeConfigData(req) {
        let org_id = parseInt(req.user_details.org.id);
        let srvc_type_id = parseInt(req.params.service_type_id);
        const query = { srvc_type_id: srvc_type_id };
        const form_data = JSON.stringify(query);
        try {
            //Returns true if the collaboration order ID exists, false otherwise
            const respData = (
                await this.db.tms_hlpr_get_config_data_for_srvc_type(form_data)
            )?.[0].tms_hlpr_get_config_data_for_srvc_type;

            return respData?.data?.config_data;
        } catch (error) {
            console.log('getSrvcTypeConfigData failed', error);
            return;
        }
    }

    getPrvdrIdOfSrvcReq(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let form_data = JSON.stringify(query);
                // console.log("form_data",form_data);
                let resp = await this.db.tms_get_prvdr_fr_srvc_req(form_data);

                var dbResp = new db_resp(resp[0].tms_get_prvdr_fr_srvc_req);

                // console.log("dbResp",dbResp);

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            dbResp.data
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(error);
                this.fatalDbError(resolve, error, true);
            }
        });
    }
    validateAndModifySbtskBatchDataFrChildAccess(req) {
        return new Promise(async (resolve, reject) => {
            try {
                const query = req.body;
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let form_data = JSON.stringify(query);
                console.log('form_data', form_data);
                let resp =
                    await this.db.tms_validate_modify_sbtsk_batch_data_fr_child_access(
                        form_data
                    );

                var dbResp = new db_resp(
                    resp[0].tms_validate_modify_sbtsk_batch_data_fr_child_access
                );

                // console.log("dbResp",dbResp);

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            dbResp.data
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(error);
                this.fatalDbError(resolve, error, true);
            }
        });
    }

    validateAndModifyUserBatchDataFrChildAccess(req) {
        return new Promise(async (resolve, reject) => {
            try {
                const query = req.body;
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let form_data = JSON.stringify(query);
                console.log('form_data', form_data);
                let resp =
                    await this.db.tms_get_role_id_from_role_code_and_modify_usr_batch_data(
                        form_data
                    );

                var dbResp = new db_resp(
                    resp[0].tms_get_role_id_from_role_code_and_modify_usr_batch_data
                );

                // console.log("dbResp",dbResp);

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            dbResp.data
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(error);
                this.fatalDbError(resolve, error, true);
            }
        });
    }

    createOrUpdateOrdersBatch(req) {
        return new Promise(async (resolve, reject) => {
            let services_model = getParamsToServiceModel(this, this.db);
            let resp = await services_model.createOrUpdateBatch(req.body, 0, 1);
            // console.log('services_model.userContext',services_model)
            resolve(resp);
        });
    }

    //This method is used to translate DB response to an API response (example is given below)
    //{"status":"success","message":"subtask created successfully","data":{"resp":[{"single_sbtsk_id":822,"single_input_data":{"sbtsk_ticket_id":"FIRS230428942191","ext_order_id":"08e3c2ce-9c46-4d55-9b4a-bb5292e4dcd1","sbtsk_type_id":3,"sbtsk_assignee":"65ef738a-5791-432d-9188-16bce946b3aa","sbtsk_priority":"Normal","sbtsk_start_day":"2023-05-08","sbtsk_start_time":"12:15PM","sbtsk_end_time":"12:30PM","sbtsk_remarks":"test","srvc_type_id":20}}]}}
    translateDbRespToApiResp(status, message, respData) {
        return {
            status: status,
            message: message,
            data: respData,
        };
    }

    //This method is used to generate FinalRespData For SbtaskCreation through Api for example as given below
    //{"resp":[{"single_sbtsk_id":822,"single_input_data":{"sbtsk_ticket_id":"FIRS230428942191","ext_order_id":"08e3c2ce-9c46-4d55-9b4a-bb5292e4dcd1","sbtsk_type_id":3,"sbtsk_assignee":"65ef738a-5791-432d-9188-16bce946b3aa","sbtsk_priority":"Normal","sbtsk_start_day":"2023-05-08","sbtsk_start_time":"12:15PM","sbtsk_end_time":"12:30PM","sbtsk_remarks":"test","srvc_type_id":20}},{"single_sbtsk_id":823,"single_input_data":{"sbtsk_ticket_id":"FIRS230428942191","ext_order_id":"08e3c2ce-9c46-4d55-9b4a-bb5292e4dcd1","sbtsk_type_id":3,"sbtsk_assignee":"89b69cb0-9fed-44b0-9ae6-ca9cea7ea16f","sbtsk_priority":"Normal","sbtsk_start_day":"2023-05-08","sbtsk_start_time":"12:15PM","sbtsk_end_time":"12:30PM","sbtsk_remarks":"test","srvc_type_id":20}}]}
    generateFinalRespDataFrSrvcReqDeletionThruApi(
        finalRespData,
        srvc_type_id,
        db_ids,
        singleEntry
    ) {
        finalRespData.resp = [
            ...finalRespData.resp,
            ...db_ids.map((db_id, index) => {
                let input_data = singleEntry.batch_data[index];
                input_data['srvc_type_id'] = srvc_type_id;
                return { input_data };
            }),
        ];
    }

    //This method is used to generate FinalRespData For SbtaskCreation through Api for example as given below
    //{"resp":[{"single_sbtsk_id":822,"single_input_data":{"sbtsk_ticket_id":"FIRS230428942191","ext_order_id":"08e3c2ce-9c46-4d55-9b4a-bb5292e4dcd1","sbtsk_type_id":3,"sbtsk_assignee":"65ef738a-5791-432d-9188-16bce946b3aa","sbtsk_priority":"Normal","sbtsk_start_day":"2023-05-08","sbtsk_start_time":"12:15PM","sbtsk_end_time":"12:30PM","sbtsk_remarks":"test","srvc_type_id":20}},{"single_sbtsk_id":823,"single_input_data":{"sbtsk_ticket_id":"FIRS230428942191","ext_order_id":"08e3c2ce-9c46-4d55-9b4a-bb5292e4dcd1","sbtsk_type_id":3,"sbtsk_assignee":"89b69cb0-9fed-44b0-9ae6-ca9cea7ea16f","sbtsk_priority":"Normal","sbtsk_start_day":"2023-05-08","sbtsk_start_time":"12:15PM","sbtsk_end_time":"12:30PM","sbtsk_remarks":"test","srvc_type_id":20}}]}
    generateFinalRespDataFrSbtaskCreationThruApi(
        finalRespData,
        srvc_type_id,
        subtask_ids,
        singleEntry
    ) {
        finalRespData.resp = [
            ...finalRespData.resp,
            ...subtask_ids.map((single_sbtsk_id, index) => {
                let single_input_data = singleEntry.batch_data[index];
                single_input_data['srvc_type_id'] = srvc_type_id;
                return { single_sbtsk_id, single_input_data };
            }),
        ];
    }
    //This method is used to generate FinalRespData For ServiceProvider assignment to an service request through Api for example as given below
    //{"resp":[{"single_srvc_req_id":423,"single_input_data":{"tms_display_code":"HMLL231201006442","ext_order_id":"08e3c2ce-9c46-4d55-9b4a-bb5292e4dcd1","service_provider_id":"2","srvc_type_id":16}}]}
    generateFinalRespDataFrSrvcPrvdrAssignToSrvcReqThruApi(
        finalRespData,
        srvc_type_id,
        srvc_req_ids,
        singleEntry
    ) {
        finalRespData.resp = [
            ...finalRespData.resp,
            ...srvc_req_ids.map((single_srvc_req_id, index) => {
                let single_input_data = singleEntry.batch_data[index];
                single_input_data['srvc_type_id'] = srvc_type_id;
                return { single_srvc_req_id, single_input_data };
            }),
        ];
    }
    //This method is used to generate FinalRespData For ServiceProvider Creation through Api for example as given below
    //{"resp":[{"single_srvc_req_id":423,"single_input_data":{"tms_display_code":"HMLL231201006442","ext_order_id":"08e3c2ce-9c46-4d55-9b4a-bb5292e4dcd1","service_provider_id":"2","srvc_type_id":16}}]}
    generateFinalRespDataFrSrvcPrvdrCreationThruApi(
        finalRespData,
        org_id,
        singleEntry
    ) {
        let single_input_data = singleEntry;
        let single_org_id = org_id;
        finalRespData.resp = [
            ...finalRespData.resp,
            { single_org_id, single_input_data },
        ];
    }

    async subtaskDeletionNotificationWorkFlow(subtasks_model, updationResp) {
        const subtaskWorkflow = getSbtskWorkflowModel(
            subtasks_model.getFreshInstance(subtasks_model)
        );
        for (const singleSrvcTypeId in updationResp) {
            let singleResp = updationResp[singleSrvcTypeId];
            if (singleResp?.entry_id_vs_query_fr_deletion) {
                await subtaskWorkflow.triggerSubtaskDeletionNotificationWorkFlow(
                    singleResp
                );
            }
            // console.log("singleResp",singleResp);
        }
    }

    validateInputForSbtaskCreation(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['org_timezone'] = users_model.getOrgTimezone(
                    this.userContext
                );

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let form_data = JSON.stringify(query);
                // console.log("form_data",form_data);
                let resp =
                    await this.db.tms_validate_input_sbtsk_creation(form_data);

                var dbResp = new db_resp(
                    resp[0].tms_validate_input_sbtsk_creation
                );

                // console.log("dbResp",dbResp);

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            dbResp.data
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(error);
                this.fatalDbError(resolve, error, true);
            }
        });
    }

    deleteServiceRequestsBatch(query) {
        return new Promise(async (resolve, reject) => {
            let services_model = getParamsToServiceModel(this, this.db);
            try {
                const subtasks_model = require('./subtasks_model');
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                const dummyQuery = Object.values(
                    query.batch_data.reduce((acc, obj) => {
                        const { srvc_type_id, ...rest } = obj;
                        if (acc[srvc_type_id]) {
                            acc[srvc_type_id].batch_data.push(rest);
                        } else {
                            acc[srvc_type_id] = {
                                ...query,
                                srvc_type_id,
                                batch_data: [rest],
                            };
                        }
                        return acc;
                    }, {})
                );
                console.log(
                    'deleteServiceRequestBatch',
                    JSON.stringify(dummyQuery)
                );
                let dbResp = (
                    await this.db.tms_delete_srvc_req_api_wrapper(
                        JSON.stringify(dummyQuery)
                    )
                )?.[0].tms_delete_srvc_req_api_wrapper;

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                const updationResp = dbResp?.data?.updation_resp;
                if (updationResp) {
                    this.subtaskDeletionNotificationWorkFlow(
                        subtasks_model,
                        updationResp
                    );
                }
                let finalRespData = { resp: [] };
                const service_workflow =
                    getServiceWorkflowModel(services_model);
                dummyQuery.forEach((singleEntry) => {
                    let srvc_type_id = singleEntry.srvc_type_id;
                    let entry_ids =
                        dbResp.data?.updation_resp?.[srvc_type_id]
                            .existing_req_ids_fr_api_call;
                    this.generateFinalRespDataFrSrvcReqDeletionThruApi(
                        finalRespData,
                        srvc_type_id,
                        entry_ids,
                        singleEntry
                    );
                    let resp = {};
                    resp['data'] = dbResp.data?.updation_resp?.[srvc_type_id];
                    service_workflow.triggerSrvcReqDeletionWorkflowFrmApi(
                        singleEntry,
                        0,
                        resp,
                        0
                    );
                });
                console.log('finalRespData', JSON.stringify(finalRespData));

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            finalRespData
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                this.fatalDbError(resolve, error, true);
            }
        });
    }

    deleteSubtaskBatch(query) {
        return new Promise(async (resolve, reject) => {
            try {
                const subtasks_model = getParamsToSubtaskModel(this, this.db);

                query['usr_id'] = users_model.getUUID(this.userContext);

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                const dummyQuery = Object.values(
                    query.batch_data.reduce((acc, obj) => {
                        const { srvc_type_id, ...rest } = obj;
                        if (acc[srvc_type_id]) {
                            acc[srvc_type_id].batch_data.push(rest);
                        } else {
                            acc[srvc_type_id] = {
                                ...query,
                                srvc_type_id,
                                batch_data: [rest],
                            };
                        }
                        return acc;
                    }, {})
                );
                console.log('deleteSubtaskBatch', JSON.stringify(dummyQuery));
                let dbResp = (
                    await this.db.tms_delete_sbtsk_api_wrapper(
                        JSON.stringify(dummyQuery)
                    )
                )?.[0].tms_delete_sbtsk_api_wrapper;

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                const updationResp = dbResp?.data?.updation_resp;
                if (updationResp) {
                    this.subtaskDeletionNotificationWorkFlow(
                        subtasks_model,
                        updationResp
                    );
                }
                let finalRespData = { resp: [] };
                const sbtsk_workflow = getSbtskWorkflowModel(subtasks_model);
                dummyQuery.forEach((singleEntry) => {
                    let srvc_type_id = singleEntry.srvc_type_id;
                    let entry_ids =
                        dbResp.data?.updation_resp?.[srvc_type_id].entry_ids;
                    this.generateFinalRespDataFrSbtaskCreationThruApi(
                        finalRespData,
                        srvc_type_id,
                        entry_ids,
                        singleEntry
                    );
                    let resp = {};
                    resp['data'] = dbResp.data?.updation_resp?.[srvc_type_id];
                    sbtsk_workflow.triggerSbtskDeletionWorkflow(
                        singleEntry,
                        0,
                        resp,
                        0
                    );
                });
                // console.log("finalRespData",JSON.stringify(finalRespData));

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            finalRespData
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                this.fatalDbError(resolve, error, true);
            }
        });
    }

    createSubtaskBatch(query) {
        return new Promise(async (resolve, reject) => {
            try {
                const subtasks_model = getParamsToSubtaskModel(this, this.db);

                query['usr_id'] = users_model.getUUID(this.userContext);
                query['org_timezone'] = users_model.getOrgTimezone(
                    this.userContext
                );
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                const dummyQuery = Object.values(
                    query.batch_data.reduce((acc, obj) => {
                        const { srvc_type_id, ...rest } = obj;
                        if (acc[srvc_type_id]) {
                            acc[srvc_type_id].batch_data.push(rest);
                        } else {
                            acc[srvc_type_id] = {
                                ...query,
                                srvc_type_id,
                                batch_data: [rest],
                            };
                        }
                        return acc;
                    }, {})
                );
                // console.log("dummyQuery",JSON.stringify(dummyQuery));
                let dbResp = (
                    await this.db.tms_create_sbtsk_api_wrapper(
                        JSON.stringify(dummyQuery)
                    )
                )?.[0].tms_create_sbtsk_api_wrapper;

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let finalRespData = { resp: [] };
                const sutaskWorkflow = getSbtskWorkflowModel(subtasks_model);
                dummyQuery.forEach((singleEntry) => {
                    let srvc_type_id = singleEntry.srvc_type_id;
                    let entry_ids =
                        dbResp.data?.updation_resp?.[srvc_type_id].entry_ids;
                    this.generateFinalRespDataFrSbtaskCreationThruApi(
                        finalRespData,
                        srvc_type_id,
                        entry_ids,
                        singleEntry
                    );
                    let resp = {};
                    resp['data'] = dbResp.data?.updation_resp?.[srvc_type_id];
                    sutaskWorkflow.trigger(singleEntry, 0, resp, 0);
                });
                // console.log("finalRespData",JSON.stringify(finalRespData));

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            finalRespData
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                this.fatalDbError(resolve, error, true);
            }
        });
    }

    getOrderDetails(req) {
        return new Promise((resolve, reject) => {
            let { tms_order_ids } = req.query;

            let tms_order_ids_array =
                typeof tms_order_ids == 'string'
                    ? [tms_order_ids]
                    : tms_order_ids;
            // resolve(
            //     new sampleOperationResp(false,
            //         JSON.stringify({}),
            //         HttpStatus.StatusCodes.OK)
            // );
            // return;
            // console.log("Trying to get overview data for ",this.srvcTypeId );
            //added new parameter
            let query = {};
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = this.srvcTypeId;
            query['display_titles'] = tms_order_ids_array;

            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            console.log('Form db', form_data);
            this.db.tms_api_brand_get_order_details(form_data).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(
                        res[0].tms_api_brand_get_order_details
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    searchOrderDetails(req) {
        return new Promise((resolve, reject) => {
            let { search_query } = req.query;
            let query = {};
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = this.srvcTypeId;
            query['search_query'] = search_query;

            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            console.log('Form db', form_data);
            this.db.tms_api_brand_search_order_details(form_data).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(
                        res[0].tms_api_brand_search_order_details
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getSystemUsrAccountFrAPIKey(api_key, childOrgId = undefined) {
        return new Promise((resolve, reject) => {
            let query = {};
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['api_key'] = api_key;
            query['child_org_id'] = childOrgId;

            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_or_create_api_usr(form_data).then(
                async (res) => {
                    var dbResp = new db_resp(res[0].tms_get_or_create_api_usr);

                    if (!dbResp.status) {
                        // console.log('Failed to update',dbResp.code)
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        var respData = dbResp.data;
                        let usr_id = respData['usr_id'];
                        let org_id = respData['org_id'];
                        let parent_org_id = respData['parent_org_id'];
                        let dummy_req_for_user_context = {
                            uuid: usr_id,
                            user_data: {
                                org: {
                                    id: org_id,
                                },
                            },
                        };
                        if (parent_org_id) {
                            dummy_req_for_user_context.user_data = {
                                ...dummy_req_for_user_context.user_data,
                                ...{
                                    parent_org: {
                                        id: parent_org_id,
                                    },
                                },
                            };
                        }
                        resolve(
                            new sampleOperationResp(
                                true,
                                dummy_req_for_user_context,
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    async getUserProto(req) {
        try {
            const user_model = require('./user_model');
            setParamsToUserModel(user_model, this, this.db);

            const [userOverview, userData] = await Promise.all([
                user_model.getUserOverviewProto(req.query),
                user_model.getViewDataFrUserForm(req.query),
            ]);

            const userProto = {
                form_proto: JSON.parse(userData?.resp),
                custom_fields:
                    JSON.parse(userOverview?.resp)?.user_custom_fields[0]?.value
                        ?.translatedFields || {},
            };

            return {
                httpStatus: 200,
                resp: userProto,
            };
        } catch (error) {
            console.error(error);
            return {
                httpStatus: 500,
                resp: { error: 'Internal server error' },
            };
        }
    }

    getUserOverViewProto(req) {
        return new Promise((resolve) => {
            const org_id = req.query.org_id;
            const user_model = require('./user_model');
            setParamsToUserModel(user_model, this, this.db);
            user_model.getUserOverviewProto(req.query).then((operationResp) => {
                org_id && (req.query.org_id = org_id);
                resolve(operationResp);
            });
        });
    }

    async createUser(req) {
        const user_model = require('./user_model');
        const parent_org_id = this.userContext.user_details?.parent_org?.id;
        setParamsToUserModel(user_model, this, this.db);

        const organization_id = parseInt(req.query.organization_id);
        //Handling If params include organization id
        if (organization_id) {
            try {
                const result =
                    await this.db.tms_verify_parent_child_relation_for_orgs(
                        parent_org_id,
                        organization_id
                    );

                const dbResp = new db_resp(
                    result[0].tms_verify_parent_child_relation_for_orgs
                );

                if (!dbResp.status) {
                    return {
                        httpStatus: HttpStatus.StatusCodes.BAD_REQUEST,
                        resp: {
                            status: false,
                            message: dbResp.code,
                            data: 'Failed',
                        },
                    };
                } else {
                    req.body.organization_id = organization_id;
                }
            } catch (error) {
                return {
                    httpStatus: HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR,
                    resp: {
                        status: false,
                        message: 'Internal Server Error',
                        data: null,
                    },
                };
            }
        }
        //Handling Create or Update operations
        try {
            const operationResp = await user_model.createOrUpdateBatch(
                req.body
            );
            if (
                !operationResp.success ||
                operationResp.resp.hasOwnProperty('hint')
            ) {
                operationResp.resp = {
                    status: false,
                    message:
                        operationResp?.resp?.hint || 'Internal Server Error',
                    data: operationResp?.resp || null,
                };
            }
            if (operationResp.success) {
                const response = JSON.parse(operationResp.resp);
                const user_ids = response.entry_ids || [];

                operationResp.resp = {
                    status: true,
                    message: 'Success',
                    data: user_ids.map((user_id, index) => {
                        //return user_id for each entry
                        const input_data = req.body.batch_data[index];
                        return { user_id, input_data };
                    }),
                };
                return operationResp;
            }
            return operationResp;
        } catch (error) {
            return {
                status: false,
                message: 'Internal Server Error',
                data: null,
            };
        }
    }

    async validateSrvcReqsDeletionDetails(req) {
        return new Promise(async (resolve, reject) => {
            let query = req.body;
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            try {
                let form_data = JSON.stringify(query);
                console.log('validateSrvcReqDeletion form data', form_data);
                let validationResp = (
                    await this.db.tms_validate_details_fr_srvc_req_deletion_api(
                        form_data
                    )
                )?.[0].tms_validate_details_fr_srvc_req_deletion_api;
                if (!validationResp.status) {
                    console.log(
                        'validateSubtaskDeletionDetails ',
                        validationResp
                    );
                    resolve(
                        new sampleOperationResp(
                            false,
                            validationResp,
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            'All good!',
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                // console.log('Error hint',error.hint);
                this.fatalDbError(resolve, error.hint);
            }
        });
    }

    async validateSubtaskDeletionDetails(req) {
        return new Promise(async (resolve, reject) => {
            let query = req.body;
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            try {
                let form_data = JSON.stringify(query);
                console.log(
                    'validateSubtaskDeletionDetails form data',
                    form_data
                );
                let validationResp = (
                    await this.db.tms_validate_details_fr_subtsk_deletion_api(
                        form_data
                    )
                )?.[0].tms_validate_details_fr_subtsk_deletion_api;
                if (!validationResp.status) {
                    console.log(
                        'validateSubtaskDeletionDetails ',
                        validationResp
                    );
                    resolve(
                        new sampleOperationResp(
                            false,
                            validationResp,
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            'All good!',
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                // console.log('Error hint',error.hint);
                this.fatalDbError(resolve, error.hint);
            }
        });
    }

    async validateSubtaskReassignmentDetails(req) {
        return new Promise(async (resolve, reject) => {
            let query = req.body;
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['is_sbtsk_updatation_fr_reschedule'] =
                req.is_sbtsk_updatation_fr_reschedule;
            query['org_timezone'] = users_model.getOrgTimezone(
                this.userContext
            );
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            try {
                let form_data = JSON.stringify(query);
                // console.log("initial form data",form_data)
                let validationResp = (
                    await this.db.tms_validate_details_fr_subtsk_reassignment_api(
                        form_data
                    )
                )?.[0].tms_validate_details_fr_subtsk_reassignment_api;
                if (!validationResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            JSON.stringify(validationResp.data),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    // return;
                }
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(validationResp.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                // console.log('Error hint',error.hint);
                this.fatalDbError(resolve, error.hint);
            }
        });
    }

    reassignSubtaskBatch(query) {
        return new Promise(async (resolve, reject) => {
            try {
                const subtasks_model = getParamsToSubtaskModel(this, this.db);

                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['org_timezone'] = users_model.getOrgTimezone(
                    this.userContext
                );
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                const dummyQuery = Object.values(
                    query.batch_data.reduce((acc, obj) => {
                        const { srvc_type_id, ...rest } = obj;
                        if (acc[srvc_type_id]) {
                            acc[srvc_type_id].batch_data.push(rest);
                        } else {
                            acc[srvc_type_id] = {
                                ...query,
                                srvc_type_id,
                                batch_data: [rest],
                            };
                        }
                        return acc;
                    }, {})
                );
                let form_data = JSON.stringify(dummyQuery);
                // console.log("form_data---->>", form_data)
                let dbResp = (
                    await this.db.tms_create_sbtsk_api_wrapper(form_data)
                )?.[0].tms_create_sbtsk_api_wrapper;

                if (!dbResp.status) {
                    // console.log("yeti came in error")
                    resolve(
                        new sampleOperationResp(
                            false,
                            JSON.stringify(dbResp.data),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    // return;
                }

                const sbtsk_workflow = getSbtskWorkflowModel(subtasks_model);
                let subtask_details_frm_resp = [];
                dummyQuery.forEach((singleEntry) => {
                    let srvc_type_id = singleEntry.srvc_type_id;
                    subtask_details_frm_resp = [
                        ...subtask_details_frm_resp,
                        ...dbResp.data?.updation_resp?.[srvc_type_id]
                            .sbtsk_details,
                    ];
                    let resp = {};
                    resp['data'] = dbResp.data?.updation_resp?.[srvc_type_id];
                    singleEntry['is_sbtsk_reassignment'] =
                        query?.is_sbtsk_updatation_fr_reschedule ? false : true;
                    sbtsk_workflow.trigger(singleEntry, 0, resp, 0);
                });

                let respArray = [];
                for (let i = 0; i < query.batch_data.length; i++) {
                    let input = query.batch_data[i];

                    for (let j = 0; j < subtask_details_frm_resp.length; j++) {
                        let sbtsk = subtask_details_frm_resp[j];

                        if (input.sbtsk_id === sbtsk.sbtsk_id) {
                            respArray.push({
                                sbtsk_details: sbtsk,
                                input_data: input,
                            });
                            break;
                        }
                    }
                }

                let finalResp = {
                    status: 'success',
                    message: query?.is_sbtsk_updatation_fr_reschedule
                        ? 'subtask reschedule successful'
                        : 'subtask reassignment successful',
                    data: { resp: respArray },
                };
                resolve(
                    new sampleOperationResp(
                        true,
                        finalResp,
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    createOrUpdateSrvcPrvdrBatch(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['is_api_call'] = 1;

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                // console.log("query",JSON.stringify(query));
                let dbResp = (
                    await this.db.tms_create_srvc_prvdr_batch(
                        JSON.stringify(query)
                    )
                )?.[0].tms_create_srvc_prvdr_batch;

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let finalRespData = { resp: [] };

                query.batch_data.forEach((singleEntry, index) => {
                    let entry_ids = dbResp.data?.entry_ids;
                    const singleEntryId = entry_ids[index];
                    this.generateFinalRespDataFrSrvcPrvdrCreationThruApi(
                        finalRespData,
                        singleEntryId,
                        singleEntry
                    );
                });
                // console.log("finalRespData",JSON.stringify(finalRespData));
                // let message = query'service provider created successfully'
                let message = query.for_updation
                    ? 'service provider updated successfully'
                    : 'service provider created successfully';
                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            message,
                            finalRespData
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                this.fatalDbError(resolve, error, true);
            }
        });
    }
    createOrUpdateSrvcReqBatch(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['is_api_call'] = 1;

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                const dummyQuery = Object.values(
                    query.batch_data.reduce((acc, obj) => {
                        const { srvc_type_id, ...rest } = obj;
                        if (acc[srvc_type_id]) {
                            acc[srvc_type_id].batch_data.push(rest);
                        } else {
                            acc[srvc_type_id] = {
                                ...query,
                                srvc_type_id,
                                batch_data: [rest],
                            };
                        }
                        return acc;
                    }, {})
                );
                // console.log("dummyQuery",JSON.stringify(dummyQuery));
                let dbResp = (
                    await this.db.tms_create_srvc_prvdr_assign_on_srvc_req_api_wrapper(
                        JSON.stringify(dummyQuery)
                    )
                )?.[0].tms_create_srvc_prvdr_assign_on_srvc_req_api_wrapper;

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let finalRespData = { resp: [] };

                dummyQuery.forEach((singleEntry) => {
                    let srvc_type_id = singleEntry.srvc_type_id;
                    let entry_ids =
                        dbResp.data?.updation_resp?.[srvc_type_id].entry_ids;
                    this.generateFinalRespDataFrSrvcPrvdrAssignToSrvcReqThruApi(
                        finalRespData,
                        srvc_type_id,
                        entry_ids,
                        singleEntry
                    );
                    let resp = {};
                    resp['data'] = dbResp.data?.updation_resp?.[srvc_type_id];
                });
                // console.log("finalRespData",JSON.stringify(finalRespData));

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            finalRespData
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                this.fatalDbError(resolve, error, true);
            }
        });
    }

    validateSrvcPrvdrCreationDetails(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['org_type'] = 'ORG_TYPE_SRVC_PRVDR';
                query['select_org_timezone'] = users_model.getOrgTimezone(
                    this.userContext
                );

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let form_data = JSON.stringify(query);
                // console.log("tms_validate_srvc_prvdr_creation_input_batch",form_data);
                let resp =
                    await this.db.tms_validate_srvc_prvdr_creation_input_batch(
                        form_data
                    );

                var dbResp = new db_resp(
                    resp[0].tms_validate_srvc_prvdr_creation_input_batch
                );

                // console.log("dbResp",dbResp);

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            dbResp.data
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(error);
                this.fatalDbError(resolve, error, true);
            }
        });
    }
    validateSrvcPrvdrUpdationDetails(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['org_type'] = 'ORG_TYPE_SRVC_PRVDR';
                query['select_org_timezone'] =
                    users_model.getOrgTimezone(this.userContext) ||
                    'Asia/kolkata';
                query['for_updation'] = true;

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let form_data = JSON.stringify(query);
                // console.log("tms_validate_srvc_prvdr_updation_input_batch",form_data);
                let resp =
                    await this.db.tms_validate_srvc_prvdr_updation_input_batch(
                        form_data
                    );

                var dbResp = new db_resp(
                    resp[0].tms_validate_srvc_prvdr_updation_input_batch
                );

                // console.log("dbResp",dbResp);

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            dbResp.data
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(error);
                this.fatalDbError(resolve, error, true);
            }
        });
    }
    validateSrvcPrvdrAssignmentDetailsToSrvcReq(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let form_data = JSON.stringify(query);
                // console.log("form_data",form_data);
                let resp =
                    await this.db.tms_validate_input_fr_srvc_prvdr_assignment_to_srvc_req(
                        form_data
                    );

                var dbResp = new db_resp(
                    resp[0].tms_validate_input_fr_srvc_prvdr_assignment_to_srvc_req
                );

                // console.log("dbResp",dbResp);

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            dbResp.data
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(error);
                this.fatalDbError(resolve, error, true);
            }
        });
    }
    getOrgSettingConfigData(req) {
        return new Promise((resolve) => {
            const org_id = req.query.org_id;
            const org_modal = require('./organisations_model');
            setParamsToOrganisationsModel(org_modal, this, this.db);
            org_modal.getOrgSettingsProto(req.query).then((operationResp) => {
                org_id && (req.query.org_id = org_id);
                resolve(operationResp);
            });
        });
    }

    async validateSubtaskUpdationDetails(req) {
        return new Promise(async (resolve, reject) => {
            let query = req.body;
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['org_timezone'] = users_model.getOrgTimezone(
                this.userContext
            );
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            try {
                let form_data = JSON.stringify(query);
                // console.log("initial form data",form_data)
                let validationResp = (
                    await this.db.tms_validate_details_fr_subtsk_updation_api(
                        form_data
                    )
                )?.[0].tms_validate_details_fr_subtsk_updation_api;
                if (!validationResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            JSON.stringify(validationResp.data),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    // return;
                }
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(validationResp.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                // console.log('Error hint',error.hint);
                this.fatalDbError(resolve, error.hint);
            }
        });
    }

    subtaskUpdationBatch(query) {
        return new Promise(async (resolve, reject) => {
            try {
                const subtasks_model = getParamsToSubtaskModel(this, this.db);

                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['is_sbtsk_updatation'] = true;
                query['org_timezone'] = users_model.getOrgTimezone(
                    this.userContext
                );
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                const dummyQuery = Object.values(
                    query.batch_data.reduce((acc, obj) => {
                        const { srvc_type_id, ...rest } = obj;
                        if (acc[srvc_type_id]) {
                            acc[srvc_type_id].batch_data.push(rest);
                        } else {
                            acc[srvc_type_id] = {
                                ...query,
                                srvc_type_id,
                                batch_data: [rest],
                            };
                        }
                        return acc;
                    }, {})
                );
                let form_data = JSON.stringify(dummyQuery);
                // console.log("form_data---->>", form_data)
                let dbResp = (
                    await this.db.tms_create_sbtsk_api_wrapper(form_data)
                )?.[0].tms_create_sbtsk_api_wrapper;

                if (!dbResp.status) {
                    // console.log("yeti came in error")
                    resolve(
                        new sampleOperationResp(
                            false,
                            JSON.stringify(dbResp.data),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    // return;
                }

                // const sbtsk_workflow = getSbtskWorkflowModel(subtasks_model)
                let subtask_details_frm_resp = [];
                dummyQuery.forEach((singleEntry) => {
                    let srvc_type_id = singleEntry.srvc_type_id;
                    subtask_details_frm_resp = [
                        ...subtask_details_frm_resp,
                        ...dbResp.data?.updation_resp?.[srvc_type_id]
                            .sbtsk_details,
                    ];
                });

                let respArray = [];
                for (let i = 0; i < query.batch_data.length; i++) {
                    let input = query.batch_data[i];

                    for (let j = 0; j < subtask_details_frm_resp.length; j++) {
                        let sbtsk = subtask_details_frm_resp[j];

                        if (input.sbtsk_id === sbtsk.sbtsk_id) {
                            respArray.push({
                                sbtsk_details: sbtsk,
                                input_data: input,
                            });
                            break;
                        }
                    }
                }

                let finalResp = {
                    status: 'success',
                    message: 'subtask updation successful',
                    data: { resp: respArray },
                };
                resolve(
                    new sampleOperationResp(
                        true,
                        finalResp,
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    

    fatalDbError(resolve, error, translateDBtoApi = false) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                !translateDBtoApi
                    ? error
                    : this.translateDbRespToApiResp('failed', error, {}),
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    set srvc_type_id(srvcTypeId) {
        this.srvcTypeId = srvcTypeId;
    }

    get srvc_type_id() {
        return this.srvcTypeId;
    }
    getFreshInstance(model) {
        const clonedInstance = new brand_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new brand_model();
